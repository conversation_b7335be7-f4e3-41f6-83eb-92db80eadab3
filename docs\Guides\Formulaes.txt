# Excel Formulas Reference - Updated for SOC Jira Tracker
# Last Updated: 2025-07-03
# Source: Refactored excel_utils.py module

## CORRECTED SLA STATUS FORMULAS

### SLA Time Thresholds (CORRECTED):
# High/Highest Priority: 1 hour
# Medium Priority: 2 hours
# Low/Lowest Priority: 23 hours

### Excel Formula for SLA Status (CORRECTED):
=IF(LEN(J22), IF(OR(AND(OR(J22="High",J22="Highest"), G22-F22<TIME(1,0,1)), AND(J22="Medium", G22-F22<TIME(2,0,1)), AND(OR(J22="Low",J22="Lowest"), G22-F22<TIME(23,0,1))), "Met", "Not Met"), "")

### Python Formula Builder (Current Implementation):
# From src/utils/excel_utils.py - ExcelFormulaBuilder.build_sla_status_formula()

def build_sla_status_formula(priority_col, notification_col, alert_col, row):
    """Build SLA status formula with CORRECTED thresholds"""
    formula = (
        f'=IF(LEN({priority_col}{row}), '
        f'IF(OR('
        f'AND(OR({priority_col}{row}="High",{priority_col}{row}="Highest"), '
        f'{notification_col}{row}-{alert_col}{row}<TIME(1,0,1)), '  # 1 hour for High
        f'AND({priority_col}{row}="Medium", '
        f'{notification_col}{row}-{alert_col}{row}<TIME(2,0,1)), '   # 2 hours for Medium
        f'AND(OR({priority_col}{row}="Low",{priority_col}{row}="Lowest"), '
        f'{notification_col}{row}-{alert_col}{row}<TIME(23,0,1))'    # 23 hours for Low
        f'), "Met", "Not Met"), "")'
    )
    return formula

### Column Reference Variables:
# These are dynamically calculated in the code:
# {chr(65 + sla_status_index)}{row}     - SLA Status column
# {chr(65 + priority_index)}{row}       - Priority column
# {chr(65 + notification_index)}{row}   - Notification Date/Time column
# {chr(65 + alert_index)}{row}          - Alert Generation Date/Time column)

## DURATION FORMULA (MSOC)

### Excel Formula for Duration Calculation:
=IF(F22="", "", IF(G22>F22, TEXT(G22-F22,"hh:mm:ss"), TEXT(G22-F22+1,"hh:mm:ss")))

### Python Implementation:
def build_duration_formula(notification_col, alert_col, row):
    """Build duration calculation formula"""
    formula = (
        f'=IF({alert_col}{row}="", "", '
        f'IF({notification_col}{row}>{alert_col}{row}, '
        f'TEXT({notification_col}{row}-{alert_col}{row},"hh:mm:ss"), '
        f'TEXT({notification_col}{row}-{alert_col}{row}+1,"hh:mm:ss")))'
    )
    return formula

===============================================================================

## PDRM FIREWALL FORMULAS

### JQL Query for PDRM Firewall:
project = "MSOC" and "customer organization[dropdown]" = "PDRM Firewall" and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth()) ORDER BY created DESC

### PDRM Excel Formulas (CORRECTED):

#### Total Time Taken Formula:
=SUM(J22-D22),"[hh]:mm:ss"

#### TM/PDRM Holding Time Formula:
=IF(O22=0, 0, IF(OR(ISBLANK(P22), P22=0), 0, TEXT((O22-P22), "[hh]:mm:ss")))

#### PDRM TL Holding Time Formula:
=SUM(J22-D22),"[hh]:mm:ss"

### Python Implementation (from excel_utils.py):

def build_total_time_formula(closing_col, start_col, row):
    """Build total time taken formula"""
    return f'=SUM({closing_col}{row}-{start_col}{row}),"[hh]:mm:ss")'

def build_holding_time_formula(total_col, tm_col, row):
    """Build holding time formula"""
    formula = (
        f'=IF({total_col}{row}=0, 0, '
        f'IF(OR(ISBLANK({tm_col}{row}), {tm_col}{row}=0), 0, '
        f'TEXT(({total_col}{row}-{tm_col}{row}), "[hh]:mm:ss")))'
    )
    return formula

### Column Mappings (Dynamic):
# These are calculated dynamically based on CSV structure:
# holding_time_index    -> Custom field (TM/PDRM Holding Time)
# total_time_index      -> Custom field (Total Time Taken)
# closing_time_index    -> Custom field (Closing Time)
# start_time_index      -> Custom field (Notification Sent out Date/Time)
# pdrm_holding_index    -> Custom field (PDRM TL Holding Time)
# tm_acknowledge_index  -> Custom field (PDRM Acknowledge Time)








