"""
Centralized Application Configuration Module for SOC Jira Tracker

This module provides centralized configuration management for all application settings,
including environment variables, hardcoded values, and business logic constants.
"""

import os
import logging
from typing import Dict, List, Optional
from pathlib import Path
from dotenv import load_dotenv


class AppConfig:
    """
    Centralized configuration management class
    """
    
    def __init__(self, env_file: str = 'config/app.env'):
        """
        Initialize configuration with environment file
        
        Args:
            env_file: Path to environment file
        """
        # Load environment variables
        load_dotenv(env_file)
        
        # Initialize all configuration sections
        self._init_jira_config()
        self._init_file_config()
        self._init_excel_config()
        self._init_gdrive_config()
        self._init_logging_config()
        
        # Validate required configuration
        self.validate()
    
    def _init_jira_config(self):
        """Initialize Jira-related configuration"""
        self.jira_ad_username = os.environ.get("JIRA_USERNAME1")
        self.jira_username = os.environ.get("JIRA_USERNAME2")
        self.jira_password = os.environ.get("JIRA_PASSWORD")
        self.jira_server = os.environ.get("JIRA_SERVER")
        self.jira_token = os.environ.get("JIRA_TOKEN")
        self.jira_filter_token = os.environ.get("JIRA_FILTER_TOKEN")
        self.token_user = os.environ.get("TOKEN_USERNAME")
        
        # Jira API headers
        self.jira_headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
    
    def _init_file_config(self):
        """Initialize file and directory configuration"""
        self.export_dir = Path("EXPORT")
        self.logs_dir = Path("logs")
        self.config_dir = Path("config")
        self.data_dir = Path("data")
        
        # File naming patterns
        self.msoc_file_pattern = "{date}_msoc_.csv"
        self.pdrm_file_pattern = "{date}_pdrm_.csv"
        self.excel_file_pattern = "modified_{type}_{date}.xlsx"
        
        # Configuration files
        self.jira_config_file = "jira_one_config.json"
        self.client_secrets_file = "client_secrets.json"
    
    def _init_excel_config(self):
        """Initialize Excel-related configuration"""
        # SLA time thresholds (in hours)
        self.sla_thresholds = {
            'high_priority': 1,      # 1 hour for High/Highest priority
            'medium_priority': 2,    # 2 hours for Medium priority  
            'low_priority': 23       # 23 hours for Low/Lowest priority
        }
        
        # Priority levels
        self.priority_levels = {
            'high': ['High', 'Highest'],
            'medium': ['Medium'],
            'low': ['Low', 'Lowest']
        }
        
        # Column orders for different export types
        self.msoc_column_order = [
            'Issue key', 'Custom field (Client Ticket Number)', 
            'Custom field (Tool Incident ID)', 'Custom field (Customer Organization)', 
            'Summary', 'Custom field (Alert Generation Date/Time)', 
            'Custom field (Notification Sent out Date/Time)', 'Custom field (Duration)', 
            'Custom field (SLA Status)', 'Priority', 'Custom field (Analyst Name)', 
            'Custom field (Incident Source/Tool)', 'Custom field (Source IP/Hostname)', 
            'Custom field (Username/Hostname/Log Source)', 'Custom field (Destination IP)', 
            'Custom field (EPF Collector IP)', 'Custom field (PDRM Fault Category)', 
            'Custom field (PDRM Acknowledge Venue)', 'Custom field (Detection/Impact)', 
            'Status', 'Custom field (Month)', 'Custom field (Resolutions Notes)', 
            'Custom field (Last Follow Up)', 'Custom field (Closing Time)', 
            'Resolution', 'Comment'
        ]
        
        self.pdrm_column_order = [
            'Issue key', 'Custom field (Client Ticket Number)', 
            'Custom field (Notification Sent out Date/Time)', 'Custom field (Last Follow Up)', 
            'Priority', 'Custom field (Analyst Name)', 'Summary', 
            'Custom field (PDRM Fault Category)', 'Custom field (Closing Time)', 
            'Custom field (PDRM Acknowledge Venue)', 'Status', 
            'Custom field (Resolutions Notes)', 'Custom field (TM/PDRM Holding Time)', 
            'Custom field (Total Time Taken)', 'Custom field (PDRM Acknowledge Time)', 
            'Custom field (PDRM TL Holding Time)', 'Custom field (Customer Organization)', 
            'Custom field (Month)', 'Comment'
        ]
    
    def _init_gdrive_config(self):
        """Initialize Google Drive configuration"""
        # Default folder ID - should be moved to environment variable
        self.gdrive_folder_id = os.environ.get(
            "GDRIVE_FOLDER_ID", 
            "1-OFA499xDXc6ktLv19ShSQ8B58sk1ibR"  # Default fallback
        )
    
    def _init_logging_config(self):
        """Initialize logging configuration"""
        self.log_level = os.environ.get("LOG_LEVEL", "INFO")
        self.log_format = '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        self.console_format = '%(levelname)s: %(message)s'
    
    def validate(self):
        """Validate that all required configuration is present"""
        required_vars = {
            'JIRA_USERNAME1': self.jira_ad_username,
            'JIRA_SERVER': self.jira_server,
            'JIRA_TOKEN': self.jira_token
        }
        
        missing_vars = [var for var, value in required_vars.items() if not value]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
    
    def get_jql_query(self, query_type: str, **kwargs) -> str:
        """
        Get predefined JQL queries
        
        Args:
            query_type: Type of query ('msoc', 'pdrm_firewall')
            **kwargs: Additional parameters for query customization
            
        Returns:
            Formatted JQL query string
        """
        if query_type == 'msoc':
            client_list = kwargs.get('client_list', '')
            return (
                f'project = "MSOC" and "customer organization[dropdown]" in ({client_list}) '
                f'and ("Notification Sent out Date/Time" >= startOfMonth() '
                f'and "Notification Sent out Date/Time" <= endOfMonth()) '
                f'ORDER BY cf[10086] DESC, created DESC'
            )
        elif query_type == 'pdrm_firewall':
            return (
                f'project = "MSOC" and "customer organization[dropdown]" = "PDRM Firewall" '
                f'and ("Notification Sent out Date/Time" >= startOfMonth() '
                f'and "Notification Sent out Date/Time" <= endOfMonth()) '
                f'ORDER BY created DESC'
            )
        else:
            raise ValueError(f"Unknown query type: {query_type}")
    
    def get_file_path(self, file_type: str, date_str: str, **kwargs) -> Path:
        """
        Get standardized file paths
        
        Args:
            file_type: Type of file ('msoc_csv', 'pdrm_csv', 'excel')
            date_str: Date string for file naming
            **kwargs: Additional parameters
            
        Returns:
            Path object for the file
        """
        if file_type == 'msoc_csv':
            filename = self.msoc_file_pattern.format(date=date_str)
            return self.export_dir / filename
        elif file_type == 'pdrm_csv':
            filename = self.pdrm_file_pattern.format(date=date_str)
            return self.export_dir / filename
        elif file_type == 'excel':
            export_type = kwargs.get('export_type', 'msoc')
            filename = self.excel_file_pattern.format(type=export_type, date=date_str)
            return self.export_dir / filename
        else:
            raise ValueError(f"Unknown file type: {file_type}")
    
    def ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [
            self.export_dir,
            self.logs_dir,
            self.logs_dir / "archive",
            self.data_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)


# Global configuration instance
_config_instance = None


def get_config() -> AppConfig:
    """
    Get the global configuration instance (singleton pattern)
    
    Returns:
        AppConfig instance
    """
    global _config_instance
    if _config_instance is None:
        _config_instance = AppConfig()
    return _config_instance


def reload_config(env_file: str = 'config/app.env') -> AppConfig:
    """
    Reload configuration (useful for testing or config changes)
    
    Args:
        env_file: Path to environment file
        
    Returns:
        New AppConfig instance
    """
    global _config_instance
    _config_instance = AppConfig(env_file)
    return _config_instance


if __name__ == "__main__":
    # Test configuration loading
    try:
        config = get_config()
        print("✅ Configuration loaded successfully")
        print(f"Jira Server: {config.jira_server}")
        print(f"Export Directory: {config.export_dir}")
        print(f"SLA Thresholds: {config.sla_thresholds}")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
