"""
Jira Utilities Module for SOC Jira Tracker

This module provides shared utility functions for Jira operations,
eliminating code duplication and providing consistent error handling.
"""

import json
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from atlassian import Jira
from jiraone import LOGIN, issue_export
from requests.auth import HTTPBasicAuth
import requests

from ..config.app_config import get_config

logger = logging.getLogger(__name__)


class JiraAuthenticationError(Exception):
    """Custom exception for Jira authentication errors"""
    pass


class JiraExportError(Exception):
    """Custom exception for Jira export errors"""
    pass


class JiraUtils:
    """
    Utility class for common Jira operations
    """
    
    def __init__(self):
        self.config = get_config()
        self._jira_connection = None
        self._jira_one_config = None
    
    def get_jira_connection(self) -> Jira:
        """
        Get authenticated Jira connection (cached)
        
        Returns:
            Authenticated Jira connection
            
        Raises:
            JiraAuthenticationError: If authentication fails
        """
        if self._jira_connection is None:
            try:
                if not all([self.config.jira_server, self.config.jira_ad_username, self.config.jira_token]):
                    raise JiraAuthenticationError("Missing required Jira configuration")
                
                self._jira_connection = Jira(
                    self.config.jira_server, 
                    self.config.jira_ad_username, 
                    self.config.jira_token, 
                    cloud=True
                )
                logger.info("Jira authentication successful")
            except Exception as e:
                logger.error(f"Jira authentication failed: {e}")
                raise JiraAuthenticationError(f"Failed to authenticate with Jira: {e}")
        
        return self._jira_connection
    
    def get_jira_one_config(self) -> Dict[str, Any]:
        """
        Load and validate Jira One configuration
        
        Returns:
            Jira One configuration dictionary
            
        Raises:
            FileNotFoundError: If config file doesn't exist
            ValueError: If config is invalid
        """
        if self._jira_one_config is None:
            config_file = self.config.config_dir / self.config.jira_config_file
            
            if not config_file.exists():
                raise FileNotFoundError(f"Jira config file not found: {config_file}")
            
            try:
                with open(config_file, 'r') as f:
                    self._jira_one_config = json.load(f)
                
                if not self._jira_one_config:
                    raise ValueError("Jira config file is empty")
                
                logger.info("Jira One configuration loaded successfully")
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in Jira config file: {e}")
        
        return self._jira_one_config
    
    def authenticate_jira_one(self) -> None:
        """
        Authenticate with Jira One API
        
        Raises:
            JiraAuthenticationError: If authentication fails
        """
        try:
            jira_config = self.get_jira_one_config()
            LOGIN(**jira_config)
            logger.info("Jira One authentication successful")
        except Exception as e:
            logger.error(f"Jira One authentication failed: {e}")
            raise JiraAuthenticationError(f"Failed to authenticate with Jira One: {e}")
    
    def export_issues_to_csv(self, jql_query: str, output_file: Path, 
                           fallback_query: Optional[str] = None) -> bool:
        """
        Export Jira issues to CSV using JQL query
        
        Args:
            jql_query: JQL query string
            output_file: Path to output CSV file
            fallback_query: Optional fallback query if main query fails
            
        Returns:
            True if export successful, False otherwise
            
        Raises:
            JiraExportError: If export fails completely
        """
        try:
            # Ensure authentication
            self.authenticate_jira_one()
            
            logger.debug(f"Executing JQL query: {jql_query}")
            
            # Attempt main export
            issue_export(jql=jql_query, final_file=str(output_file))
            
            # Validate export file was created and has content
            if not output_file.exists():
                raise JiraExportError(f"Export file was not created: {output_file}")
            
            if output_file.stat().st_size == 0:
                raise JiraExportError(f"Export file is empty: {output_file}")
            
            logger.info(f"Export completed successfully: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error in main export: {str(e)}")
            
            # Try fallback query if provided
            if fallback_query:
                logger.info("Trying fallback query...")
                try:
                    issue_export(jql=fallback_query, final_file=str(output_file))
                    logger.info("Fallback query succeeded")
                    return True
                except Exception as fallback_error:
                    logger.error(f"Fallback query also failed: {fallback_error}")
            
            raise JiraExportError(f"Export failed: {e}")
    
    def get_favorite_filters(self) -> list:
        """
        Get favorite filters from Jira
        
        Returns:
            List of favorite filter tuples (name, jql, search_url, view_url)
            
        Raises:
            JiraAuthenticationError: If authentication fails
            requests.RequestException: If API request fails
        """
        try:
            if not all([self.config.jira_server, self.config.jira_username, self.config.jira_filter_token]):
                raise JiraAuthenticationError("Missing required Jira filter configuration")
            
            fav_filters_url = f"{self.config.jira_server}/rest/api/3/filter/favourite"
            filters_auth = HTTPBasicAuth(self.config.jira_username, self.config.jira_filter_token)
            
            response = requests.get(
                fav_filters_url,
                headers=self.config.jira_headers,
                auth=filters_auth
            )
            
            response.raise_for_status()
            
            fav_filter_list = []
            if response.status_code == 200:
                favourites = response.json()
                
                for fav in favourites:
                    fav_filter_name = fav['name']
                    jql_query = fav['jql']
                    fav_search_url = fav['searchUrl']
                    fav_view_url = fav['viewUrl']
                    fav_filter_list.append((fav_filter_name, jql_query, fav_search_url, fav_view_url))
                
                logger.info(f"Successfully retrieved {len(fav_filter_list)} favorite filters")
            else:
                logger.warning("No favorite filters found")
            
            return fav_filter_list
            
        except requests.RequestException as e:
            logger.error(f"Failed to obtain favorite filters: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in get_favorite_filters: {e}")
            raise
    
    def get_server_info(self) -> Dict[str, Any]:
        """
        Get Jira server information
        
        Returns:
            Dictionary containing server information
        """
        try:
            jira = self.get_jira_connection()
            server_info = jira.server_info()
            logger.info("Server info retrieved successfully")
            return server_info
        except Exception as e:
            logger.error(f"Failed to get server info: {e}")
            raise
    
    def list_projects(self) -> list:
        """
        List all available Jira projects
        
        Returns:
            List of project dictionaries
        """
        try:
            jira = self.get_jira_connection()
            projects = jira.projects()
            
            if not projects:
                logger.warning("No projects found")
                return []
            
            logger.info(f"Found {len(projects)} projects")
            return projects
            
        except Exception as e:
            logger.error(f"Failed to list projects: {e}")
            raise


# Global utility instance
_jira_utils_instance = None


def get_jira_utils() -> JiraUtils:
    """
    Get the global JiraUtils instance (singleton pattern)
    
    Returns:
        JiraUtils instance
    """
    global _jira_utils_instance
    if _jira_utils_instance is None:
        _jira_utils_instance = JiraUtils()
    return _jira_utils_instance


# Convenience functions for backward compatibility
def jira_api_auth():
    """Create authenticated Jira connection (backward compatibility)"""
    return get_jira_utils().get_jira_connection()


def validate_jira_config():
    """Validate Jira configuration (backward compatibility)"""
    return get_jira_utils().get_jira_one_config()


def obtain_fav_filter():
    """Obtain favorite filters (backward compatibility)"""
    filters = get_jira_utils().get_favorite_filters()
    if filters:
        print(filters[0])
        print(f"type : {type(filters[0])}")


def list_jira_projects():
    """List Jira projects (backward compatibility)"""
    projects = get_jira_utils().list_projects()
    for project in projects:
        print(project['key'])


def api_version():
    """Get Jira API version (backward compatibility)"""
    server_info = get_jira_utils().get_server_info()
    print(server_info.get('version', 'Unknown'))


if __name__ == "__main__":
    # Test utilities
    try:
        utils = get_jira_utils()
        print("✅ Jira utilities initialized successfully")
        
        # Test connection
        jira = utils.get_jira_connection()
        print("✅ Jira connection successful")
        
    except Exception as e:
        print(f"❌ Error: {e}")
