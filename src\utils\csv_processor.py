"""
CSV Processing Utilities for SOC Jira Tracker

This module provides utilities for CSV file processing, column manipulation,
and data transformation operations.
"""

import os
import csv
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class CSVProcessingError(Exception):
    """Custom exception for CSV processing errors"""
    pass


class CSVProcessor:
    """
    Utility class for CSV file processing operations
    """
    
    def __init__(self):
        pass
    
    def remove_column_if_exists(self, csv_file_path: str, column_name: str) -> bool:
        """
        Remove a single column from CSV file if it exists
        
        Args:
            csv_file_path: Path to CSV file
            column_name: Name of column to remove
            
        Returns:
            True if column was removed, False if not found
            
        Raises:
            CSVProcessingError: If processing fails
        """
        temp_file_path = csv_file_path + '.tmp'
        found_column = False
        index_to_remove = -1

        try:
            with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile, \
                 open(temp_file_path, 'w', newline='', encoding='utf-8') as temp_csvfile:
                reader = csv.reader(csvfile)
                writer = csv.writer(temp_csvfile)

                for i, row in enumerate(reader):
                    if i == 0:  # Header row
                        if column_name in row:
                            found_column = True
                            index_to_remove = row.index(column_name)
                            new_row = [cell for j, cell in enumerate(row) if j != index_to_remove]
                            writer.writerow(new_row)
                        else:
                            writer.writerow(row)
                    else:
                        if found_column:
                            new_row = [cell for j, cell in enumerate(row) if j != index_to_remove]
                            writer.writerow(new_row)
                        else:
                            writer.writerow(row)

            if found_column:
                os.remove(csv_file_path)
                os.rename(temp_file_path, csv_file_path)
                logger.info(f"Column '{column_name}' removed from CSV file")
            else:
                os.remove(temp_file_path)
                logger.info(f"Column '{column_name}' not found in CSV file")
                
            return found_column
                
        except Exception as e:
            # Clean up temp file if it exists
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            logger.error(f"Error removing column '{column_name}': {e}")
            raise CSVProcessingError(f"Failed to remove column '{column_name}': {e}")
    
    def remove_columns_if_exist(self, csv_file_path: str, columns_to_remove: List[str]) -> List[str]:
        """
        Remove multiple columns from CSV file if they exist
        
        Args:
            csv_file_path: Path to CSV file
            columns_to_remove: List of column names to remove
            
        Returns:
            List of columns that were actually removed
            
        Raises:
            CSVProcessingError: If processing fails
        """
        temp_file_path = csv_file_path + '.tmp'
        found_columns = {col.strip(): False for col in columns_to_remove}
        columns_removed = []

        try:
            with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile, \
                 open(temp_file_path, 'w', newline='', encoding='utf-8') as temp_csvfile:
                reader = csv.reader(csvfile)
                writer = csv.writer(temp_csvfile)

                # Read header
                header = next(reader)
                header = [col.strip() for col in header]  # Strip whitespace from header

                # Check which columns exist
                for col in columns_to_remove:
                    if col.strip() in header:
                        found_columns[col.strip()] = True
                        columns_removed.append(col.strip())

                # Write new header
                new_header = [header[i] for i in range(len(header)) 
                             if header[i] not in [col.strip() for col in columns_to_remove]]
                writer.writerow(new_header)

                # Process data rows
                for row in reader:
                    new_row = [row[i] for i in range(len(row)) 
                              if i < len(header) and header[i] not in [col.strip() for col in columns_to_remove]]
                    writer.writerow(new_row)

            if columns_removed:
                os.remove(csv_file_path)
                os.rename(temp_file_path, csv_file_path)
                logger.info(f"Columns removed: {', '.join(columns_removed)}")
            else:
                os.remove(temp_file_path)
                logger.info(f"No columns found to remove from: {columns_to_remove}")
                
            return columns_removed
                
        except Exception as e:
            # Clean up temp file if it exists
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            logger.error(f"Error removing columns {columns_to_remove}: {e}")
            raise CSVProcessingError(f"Failed to remove columns: {e}")
    
    def get_headers(self, csv_file_path: str) -> List[str]:
        """
        Get headers from CSV file
        
        Args:
            csv_file_path: Path to CSV file
            
        Returns:
            List of header names
            
        Raises:
            CSVProcessingError: If file cannot be read
        """
        try:
            with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.reader(csvfile)
                headers = next(reader)
            return headers
        except Exception as e:
            logger.error(f"Error reading headers from {csv_file_path}: {e}")
            raise CSVProcessingError(f"Failed to read headers: {e}")
    
    def rearrange_columns(self, input_file: str, output_file: str, desired_column_order: List[str]) -> bool:
        """
        Rearrange columns in CSV file according to desired order
        
        Args:
            input_file: Path to input CSV file
            output_file: Path to output CSV file
            desired_column_order: List of column names in desired order
            
        Returns:
            True if successful
            
        Raises:
            CSVProcessingError: If processing fails
        """
        try:
            import pandas as pd
            
            df = pd.read_csv(input_file)
            
            missing_columns = [col for col in desired_column_order if col not in df.columns]
            if missing_columns:
                logger.warning(f"Missing columns in input file: {missing_columns}")
                # Use only columns that exist
                available_columns = [col for col in desired_column_order if col in df.columns]
                df = df[available_columns]
            else:
                df = df[desired_column_order]
            
            df.to_csv(output_file, index=False)
            logger.info(f"Columns rearranged successfully: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error rearranging columns: {e}")
            raise CSVProcessingError(f"Failed to rearrange columns: {e}")
    
    def modify_resolution_column(self, input_file: str, output_file: str, column_name: str) -> bool:
        """
        Modify resolution column by extracting last part after semicolon
        
        Args:
            input_file: Path to input CSV file
            output_file: Path to output CSV file
            column_name: Name of column to modify
            
        Returns:
            True if successful
            
        Raises:
            CSVProcessingError: If processing fails
        """
        try:
            with open(input_file, 'r', newline='', encoding='utf-8') as infile, \
                 open(output_file, 'w', newline='', encoding='utf-8') as outfile:
                reader = csv.DictReader(infile)
                fieldnames = reader.fieldnames
                writer = csv.DictWriter(outfile, fieldnames=fieldnames)
                writer.writeheader()

                for row in reader:
                    if column_name in row:
                        resolution = row[column_name]
                        # Split the content based on ";" and get the last part
                        parts = resolution.rsplit(';', 1)
                        if len(parts) > 1:
                            modified_resolution = parts[-1].strip()
                            row[column_name] = modified_resolution
                    writer.writerow(row)
            
            logger.info(f"Resolution column '{column_name}' modified successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error modifying resolution column: {e}")
            raise CSVProcessingError(f"Failed to modify resolution column: {e}")
    
    def convert_date_format(self, input_file: str, output_file: str, 
                          date_column: str = 'Custom field (Notification Sent out Date/Time)', 
                          month_column: str = 'Custom field (Month)') -> bool:
        """
        Convert date format and populate month column
        
        Args:
            input_file: Path to input CSV file
            output_file: Path to output CSV file
            date_column: Name of date column to read from
            month_column: Name of month column to populate
            
        Returns:
            True if successful
            
        Raises:
            CSVProcessingError: If processing fails
        """
        def parse_date(date_str):
            return datetime.strptime(date_str, "%d/%b/%y %I:%M %p")

        def format_date(date_obj):
            return str(date_obj.strftime("%b %Y"))

        # Try different encodings
        encodings_to_try = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']

        for encoding in encodings_to_try:
            try:
                logger.debug(f"Trying to read CSV with {encoding} encoding...")
                with open(input_file, 'r', encoding=encoding) as infile, \
                     open(output_file, 'w', newline='', encoding='utf-8') as outfile:
                    reader = csv.DictReader(infile)
                    fieldnames = reader.fieldnames
                    writer = csv.DictWriter(outfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for row in reader:
                        if row.get(month_column):  # Check if month column has a value
                            date_str = row.get(date_column, '')
                            if date_str:
                                try:
                                    date_obj = parse_date(date_str)
                                    formatted_date = format_date(date_obj)
                                    row[month_column] = formatted_date
                                except ValueError:
                                    logger.warning(f"Could not parse date: {date_str}")
                        writer.writerow(row)

                logger.info(f"Date format conversion successful with {encoding} encoding")
                return True

            except UnicodeDecodeError:
                logger.debug(f"Failed with {encoding} encoding, trying next...")
                continue
            except Exception as e:
                logger.error(f"Error with {encoding} encoding: {e}")
                continue

        # If we get here, all encodings failed
        error_msg = "Failed to process CSV file with all attempted encodings"
        logger.error(error_msg)
        raise CSVProcessingError(error_msg)
    
    def find_csv_files(self, directory: str) -> List[str]:
        """
        Find all CSV files in a directory
        
        Args:
            directory: Directory path to search
            
        Returns:
            List of CSV file names
        """
        csv_files = []
        if os.path.exists(directory):
            for file in os.listdir(directory):
                if file.endswith(".csv"):
                    csv_files.append(file)
        else:
            logger.warning(f"Directory does not exist: {directory}")
        
        return csv_files


# Global processor instance
_csv_processor_instance = None


def get_csv_processor() -> CSVProcessor:
    """
    Get the global CSVProcessor instance (singleton pattern)
    
    Returns:
        CSVProcessor instance
    """
    global _csv_processor_instance
    if _csv_processor_instance is None:
        _csv_processor_instance = CSVProcessor()
    return _csv_processor_instance


if __name__ == "__main__":
    # Test CSV processor
    processor = get_csv_processor()
    print("✅ CSV processor initialized successfully")
