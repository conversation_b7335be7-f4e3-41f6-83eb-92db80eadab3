"""
Integration tests for SOC Jira Tracker

This module contains integration tests that verify the complete workflows
and interactions between different components of the system.
"""

import unittest
import tempfile
import shutil
import os
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.config.app_config import AppConfig, reload_config
from src.utils.jira_utils import JiraUtils
from src.utils.csv_processor import CSVProcessor
from src.utils.excel_utils import ExcelProcessor
from src.utils.logging_utils import LoggingManager


class TestIntegrationWorkflows(unittest.TestCase):
    """Integration tests for complete workflows"""
    
    def setUp(self):
        """Set up test environment"""
        # Create temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        self.export_dir = Path(self.test_dir) / "EXPORT"
        self.export_dir.mkdir(exist_ok=True)
        
        # Create test CSV data
        self.test_csv_data = [
            "Issue key,Summary,Priority,Custom field (SLA Status),Custom field (Duration)",
            "TEST-001,Test Issue 1,High,,",
            "TEST-002,Test Issue 2,Medium,,",
            "TEST-003,Test Issue 3,Low,,"
        ]
        
        # Create test CSV file
        self.test_csv_file = self.export_dir / "test_data.csv"
        with open(self.test_csv_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(self.test_csv_data))
    
    def tearDown(self):
        """Clean up test environment"""
        # Remove temporary directory
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    @patch.dict(os.environ, {
        'JIRA_SERVER': 'https://test-jira.com',
        'JIRA_USERNAME1': 'test_user',
        'JIRA_TOKEN': 'test_token',
        'LOG_LEVEL': 'DEBUG'
    })
    def test_configuration_loading(self):
        """Test that configuration loads correctly with environment variables"""
        # Reload config with test environment
        config = reload_config()
        
        self.assertEqual(config.jira_server, 'https://test-jira.com')
        self.assertEqual(config.jira_ad_username, 'test_user')
        self.assertEqual(config.jira_token, 'test_token')
        self.assertEqual(config.log_level, 'DEBUG')
        
        # Test that directories are created
        config.ensure_directories()
        self.assertTrue(config.export_dir.exists())
        self.assertTrue(config.logs_dir.exists())
    
    def test_csv_processing_workflow(self):
        """Test complete CSV processing workflow"""
        processor = CSVProcessor()
        
        # Test reading headers
        headers = processor.get_headers(str(self.test_csv_file))
        expected_headers = [
            "Issue key", "Summary", "Priority", 
            "Custom field (SLA Status)", "Custom field (Duration)"
        ]
        self.assertEqual(headers, expected_headers)
        
        # Test column rearrangement
        new_order = ["Priority", "Issue key", "Summary"]
        output_file = self.export_dir / "rearranged.csv"
        
        result = processor.rearrange_columns(
            str(self.test_csv_file), 
            str(output_file), 
            new_order
        )
        
        self.assertTrue(result)
        self.assertTrue(output_file.exists())
        
        # Verify rearranged headers
        rearranged_headers = processor.get_headers(str(output_file))
        self.assertEqual(rearranged_headers, new_order)
    
    def test_excel_processing_workflow(self):
        """Test Excel processing workflow"""
        processor = ExcelProcessor()
        
        # Create test CSV with required MSOC columns
        msoc_csv_data = [
            "Issue key,Summary,Priority,Custom field (SLA Status),Custom field (Duration),"
            "Custom field (Notification Sent out Date/Time),Custom field (Alert Generation Date/Time)",
            "TEST-001,Test Issue 1,High,,,2024-01-01 10:00:00,2024-01-01 09:00:00",
            "TEST-002,Test Issue 2,Medium,,,2024-01-01 12:00:00,2024-01-01 10:00:00"
        ]
        
        msoc_csv_file = self.export_dir / "msoc_test.csv"
        with open(msoc_csv_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(msoc_csv_data))
        
        excel_file = self.export_dir / "test_output.xlsx"
        
        # Test Excel generation
        result = processor.transform_msoc_csv_to_excel(
            str(msoc_csv_file), 
            str(excel_file)
        )
        
        self.assertTrue(result)
        self.assertTrue(excel_file.exists())
        
        # Verify file is not empty
        self.assertGreater(excel_file.stat().st_size, 0)
    
    @patch('src.utils.jira_utils.LOGIN')
    @patch('src.utils.jira_utils.issue_export')
    def test_jira_export_workflow(self, mock_issue_export, mock_login):
        """Test Jira export workflow with mocked dependencies"""
        # Mock successful authentication and export
        mock_login.return_value = None
        mock_issue_export.return_value = None
        
        # Create mock config
        with patch.dict(os.environ, {
            'JIRA_SERVER': 'https://test-jira.com',
            'JIRA_USERNAME1': 'test_user',
            'JIRA_TOKEN': 'test_token'
        }):
            jira_utils = JiraUtils()
            
            # Test export
            test_query = 'project = "TEST"'
            output_file = self.export_dir / "jira_export.csv"
            
            # Create empty file to simulate successful export
            output_file.touch()
            output_file.write_text("Issue key,Summary\nTEST-001,Test Issue")
            
            result = jira_utils.export_issues_to_csv(test_query, output_file)
            
            self.assertTrue(result)
            self.assertTrue(output_file.exists())
            mock_login.assert_called_once()
            mock_issue_export.assert_called_once()
    
    def test_logging_system_integration(self):
        """Test logging system integration"""
        logging_manager = LoggingManager()
        
        # Test logging setup
        root_logger = logging_manager.setup_logging()
        self.assertIsNotNone(root_logger)
        
        # Test named logger creation
        test_logger = logging_manager.get_logger("test_module")
        self.assertIsNotNone(test_logger)
        
        # Test operation logger
        operation_logger = logging_manager.create_operation_logger("test_operation")
        self.assertIsNotNone(operation_logger)
    
    def test_file_cleanup_workflow(self):
        """Test file cleanup and management"""
        processor = CSVProcessor()
        
        # Create multiple test files
        test_files = []
        for i in range(3):
            test_file = self.export_dir / f"test_{i}.csv"
            test_file.write_text(f"data_{i}")
            test_files.append(test_file)
        
        # Verify files exist
        for test_file in test_files:
            self.assertTrue(test_file.exists())
        
        # Test finding CSV files
        csv_files = processor.find_csv_files(str(self.export_dir))
        self.assertEqual(len(csv_files), 4)  # 3 test files + 1 from setUp
    
    @patch('src.utils.jira_utils.Jira')
    def test_error_handling_workflow(self, mock_jira_class):
        """Test error handling in workflows"""
        # Mock Jira to raise an exception
        mock_jira_class.side_effect = Exception("Connection failed")
        
        with patch.dict(os.environ, {
            'JIRA_SERVER': 'https://test-jira.com',
            'JIRA_USERNAME1': 'test_user',
            'JIRA_TOKEN': 'test_token'
        }):
            jira_utils = JiraUtils()
            
            # Test that exception is properly handled
            with self.assertRaises(Exception):
                jira_utils.get_jira_connection()
    
    def test_configuration_validation_workflow(self):
        """Test configuration validation workflow"""
        # Test with missing required environment variables
        with patch.dict(os.environ, {}, clear=True):
            with self.assertRaises(ValueError):
                AppConfig()
        
        # Test with valid configuration
        with patch.dict(os.environ, {
            'JIRA_SERVER': 'https://test-jira.com',
            'JIRA_USERNAME1': 'test_user',
            'JIRA_TOKEN': 'test_token'
        }):
            config = AppConfig()
            self.assertIsNotNone(config)
            
            # Test JQL query generation
            msoc_query = config.get_jql_query('msoc', client_list='TEST, DEMO')
            self.assertIn('project = "MSOC"', msoc_query)
            self.assertIn('TEST, DEMO', msoc_query)
            
            pdrm_query = config.get_jql_query('pdrm_firewall')
            self.assertIn('PDRM Firewall', pdrm_query)
    
    def test_data_transformation_workflow(self):
        """Test complete data transformation workflow"""
        processor = CSVProcessor()
        
        # Create test data with resolution column
        test_data_with_resolution = [
            "Issue key,Summary,Resolution",
            "TEST-001,Test Issue 1,Part1;Part2;Final Resolution",
            "TEST-002,Test Issue 2,Single Resolution"
        ]
        
        input_file = self.export_dir / "input_with_resolution.csv"
        output_file = self.export_dir / "output_modified.csv"
        
        with open(input_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(test_data_with_resolution))
        
        # Test resolution column modification
        result = processor.modify_resolution_column(
            str(input_file), 
            str(output_file), 
            'Resolution'
        )
        
        self.assertTrue(result)
        self.assertTrue(output_file.exists())
        
        # Verify modification worked
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn('Final Resolution', content)
            self.assertNotIn('Part1;Part2;', content)


class TestSystemIntegration(unittest.TestCase):
    """System-level integration tests"""
    
    def setUp(self):
        """Set up system test environment"""
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up system test environment"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    @patch.dict(os.environ, {
        'JIRA_SERVER': 'https://test-jira.com',
        'JIRA_USERNAME1': 'test_user',
        'JIRA_TOKEN': 'test_token'
    })
    def test_system_initialization(self):
        """Test complete system initialization"""
        # Test that all components can be initialized
        config = reload_config()
        self.assertIsNotNone(config)
        
        jira_utils = JiraUtils()
        self.assertIsNotNone(jira_utils)
        
        csv_processor = CSVProcessor()
        self.assertIsNotNone(csv_processor)
        
        excel_processor = ExcelProcessor()
        self.assertIsNotNone(excel_processor)
        
        logging_manager = LoggingManager()
        self.assertIsNotNone(logging_manager)


if __name__ == '__main__':
    # Run integration tests
    unittest.main(verbosity=2)
