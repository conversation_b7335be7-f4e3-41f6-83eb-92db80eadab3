import os
import requests
import urllib3
import json
import csv
import logging
import sys
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple

from datetime import datetime, date
from atlassian import <PERSON><PERSON>
from requests.auth import HTTPBasicAuth
from dotenv import load_dotenv
from jiraone import LOGIN, issue_export

from ..utils.gdrive_method import *
from ..config.client_config import ClientConfig, PDRMFirewallDataStructure
from ..config.app_config import get_config
from ..utils.pdrm_firewall_formatter import PDRMFirewallFormatter
from ..utils.jira_utils import get_jira_utils
from ..utils.csv_processor import get_csv_processor
from ..utils.excel_utils import get_excel_processor

# pandas import removed - now handled by utility modules

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Import logging utilities
from ..utils.logging_utils import setup_logging, get_logger

# Initialize logging
logger = setup_logging()

# Initialize centralized configuration
try:
    config = get_config()
    config.ensure_directories()
    logger.info("Configuration loaded successfully")
except ValueError as e:
    logger.error(f"Configuration error: {e}")
    sys.exit(1)

# Initialize utility instances
jira_utils = get_jira_utils()
csv_processor = get_csv_processor()
excel_processor = get_excel_processor()

def jira_api_auth() -> Jira:
    """Create authenticated Jira connection (backward compatibility)"""
    return jira_utils.get_jira_connection()

def list_jira_projects() -> None:
    """List all available Jira projects (backward compatibility)"""
    projects = jira_utils.list_projects()
    for project in projects:
        print(project['key'])

def api_version() -> None:
    """Get Jira API version (backward compatibility)"""
    server_info = jira_utils.get_server_info()
    version = server_info.get('version', 'Unknown')
    logger.info(f"Jira API version: {version}")
    print(version)

def obtain_fav_filter() -> None:
    """Obtain favorite filters (backward compatibility)"""
    filters = jira_utils.get_favorite_filters()
    if filters:
        print(filters[0])
        print(f"type : {type(filters[0])}")

def return_server_info() -> Dict[str, Any]:
    """Get Jira server information (backward compatibility)"""
    server_info = jira_utils.get_server_info()
    logger.info("Server info retrieved successfully")
    return server_info

def ensure_export_directory() -> Path:
    """Ensure EXPORT directory exists"""
    export_dir = Path("EXPORT")
    export_dir.mkdir(exist_ok=True)
    logger.info(f"Export directory ensured: {export_dir.absolute()}")
    return export_dir

def validate_jira_config() -> Dict[str, Any]:
    """Validate Jira configuration (backward compatibility)"""
    return jira_utils.get_jira_one_config()

def msoc_export_to_csv() -> None:
    """Export MSOC data to CSV with comprehensive error handling"""
    try:
        # Ensure export directory exists
        export_dir = ensure_export_directory()

        # Use safe client list to avoid 400 errors from non-existent clients
        safe_clients = ClientConfig.get_safe_msoc_clients()
        client_list = ClientConfig.get_jql_client_list(safe_clients)
        jql_query = config.get_jql_query('msoc', client_list=client_list)

        # Debug: Print the JQL query to check for issues
        logger.debug(f"MSOC JQL Query: {jql_query}")
        logger.info(f"Using {len(safe_clients)} safe clients for MSOC export")

        # Show pending clients that are not included
        pending_clients = ClientConfig.get_pending_clients()
        if pending_clients:
            logger.info(f"{len(pending_clients)} clients pending addition to Jira: {', '.join(pending_clients)}")

        # Generate export file path
        current_datetime = datetime.now()
        date_str = current_datetime.strftime("%d%m%y")
        export_file_path = config.get_file_path('msoc_csv', date_str)

        # Fallback query
        fallback_query = 'project = "MSOC" and "customer organization[dropdown]" in (MAG, PDRM, Astro, Firefly, EPF) and ("Notification Sent out Date/Time" >= startOfMonth() and "Notification Sent out Date/Time" <= endOfMonth()) ORDER BY created DESC'

        # Use Jira utilities for export
        success = jira_utils.export_issues_to_csv(jql_query, export_file_path, fallback_query)

        if success:
            logger.info(f"MSOC export completed successfully: {export_file_path}")
        else:
            raise Exception("MSOC export failed")

    except Exception as e:
        logger.error(f"Critical error in MSOC export process: {e}")
        raise

def pdrm_fw_export_to_csv():
    """Export PDRM Firewall data to CSV with error handling"""
    try:
        # Ensure export directory exists
        export_dir = ensure_export_directory()

        jql_query = config.get_jql_query('pdrm_firewall')

        # Generate export file path
        current_datetime = datetime.now()
        date_str = current_datetime.strftime("%d%m%y")
        export_file_path = config.get_file_path('pdrm_csv', date_str)

        logger.debug(f"PDRM JQL Query: {jql_query}")

        # Use Jira utilities for export
        success = jira_utils.export_issues_to_csv(jql_query, export_file_path)

        if success:
            logger.info(f"PDRM export completed successfully: {export_file_path}")
        else:
            raise Exception("PDRM export failed")

    except Exception as e:
        logger.error(f"Critical error in PDRM export process: {e}")
        raise


def pdrm_fw_export_structured():
    """
    Export PDRM Firewall data in the new structured format with improved error handling
    """
    try:
        # First export using the standard method
        pdrm_fw_export_to_csv()

        # Get the current datetime for file naming
        current_datetime = datetime.now()
        curr_fmt_datetime = current_datetime.strftime("%d%m%y")

        # Use Path objects for better file handling
        export_dir = Path("EXPORT")
        raw_export_file = export_dir / f"{curr_fmt_datetime}_pdrm_.csv"
        structured_csv_file = export_dir / f"pdrm_firewall_structured_{curr_fmt_datetime}.csv"
        structured_json_file = export_dir / f"pdrm_firewall_structured_{curr_fmt_datetime}.json"

        # Check if raw export file exists
        if not raw_export_file.exists():
            error_msg = f"Raw export file not found: {raw_export_file}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        # Read the raw export data
        raw_data = []
        try:
            with open(raw_export_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                raw_data = list(reader)
        except Exception as e:
            logger.error(f"Failed to read raw export file: {e}")
            raise

        if not raw_data:
            logger.warning("No data found in raw export file")
            return None, None

        logger.info(f"Read {len(raw_data)} records from raw export file")

        # Initialize the formatter
        formatter = PDRMFirewallFormatter()

        # Transform the data to PDRM Firewall structure
        structured_data = formatter.transform_jira_data_to_pdrm_format(raw_data)

        # Validate the data
        validation_results = formatter.validate_data(structured_data)
        if not validation_results['valid']:
            logger.warning("Data validation failed:")
            for error in validation_results['errors']:
                logger.warning(f"  Record {error['record']}: Missing fields {error['fields']}")

        # Export to CSV and JSON (convert Path to string)
        formatter.export_to_csv(structured_data, str(structured_csv_file))
        formatter.export_to_json(structured_data, str(structured_json_file))

        logger.info(f"PDRM Firewall structured export completed:")
        logger.info(f"  CSV: {structured_csv_file}")
        logger.info(f"  JSON: {structured_json_file}")
        logger.info(f"  Records processed: {len(structured_data)}")

        return str(structured_csv_file), str(structured_json_file)

    except FileNotFoundError as e:
        logger.error(f"File not found during PDRM structured export: {e}")
        return None, None
    except Exception as e:
        logger.error(f"Error during PDRM Firewall structured export: {e}")
        return None, None


def find_csv_files(dir: str):
    """Find CSV files in directory (backward compatibility)"""
    return csv_processor.find_csv_files(dir)


def remove_single_column_if_exists(csv_file_path: str, column_name: str):
    """Remove a single column from CSV file if it exists (backward compatibility)"""
    return csv_processor.remove_column_if_exists(csv_file_path, column_name)


def remove_columns_if_exist(csv_file_path: str, columns_to_remove: list):
    """Remove multiple columns from CSV file (backward compatibility)"""
    return csv_processor.remove_columns_if_exist(csv_file_path, columns_to_remove)


def get_headers(csv_file_path: str) -> list:
    """Get CSV headers (backward compatibility)"""
    return csv_processor.get_headers(csv_file_path)


def compare_lists(list1: list, list2: list):
    """Compare two lists and return unique items in list2"""
    unique_in_list2 = [item for item in list2 if item not in list1]
    return unique_in_list2


def rearrange_columns(input_file: str, output_file: str, desired_column_order: list):
    """Rearrange CSV columns (backward compatibility)"""
    return csv_processor.rearrange_columns(input_file, output_file, desired_column_order)


def transform_csv_to_excel_with_formulas(csv_file: str, excel_file: str):
    """Transform CSV to Excel with formulas (backward compatibility)"""
    return excel_processor.transform_msoc_csv_to_excel(csv_file, excel_file)


def transform__pdrm_csv_to_excel_with_formulas(csv_file: str, excel_file: str):
    """Transform PDRM CSV to Excel with formulas (backward compatibility)"""
    return excel_processor.transform_pdrm_csv_to_excel(csv_file, excel_file)


def convert_date_format(input_file, output_file, date_column='Custom field (Notification Sent out Date/Time)', month_column='Custom field (Month)'):
    """Convert date format in CSV (backward compatibility)"""
    return csv_processor.convert_date_format(input_file, output_file, date_column, month_column)


def delete_current_day_csv_files(directory):
    # Get the current date
    current_date = date.today()

    # Iterate over all files in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.csv'):
            filepath = os.path.join(directory, filename)
            # Get the modification time of the file
            modification_time = os.path.getmtime(filepath)
            modification_date = datetime.fromtimestamp(modification_time).date()
            # Check if the modification date is the same as the current date
            if modification_date == current_date:
                # Delete the file
                os.remove(filepath)
                print(f"Deleted: {filepath}")


def upload_to_drive(file_path: str):
    """Upload file to Google Drive"""
    try:
        file_to_be_uploaded_paths = [file_path]
        destination_folder_id = config.gdrive_folder_id
        uploaded_files = upload_files_to_drive(file_to_be_uploaded_paths, destination_folder_id)

        # Log upload results
        for file_info in uploaded_files:
            logger.info(f"Uploaded: {file_info['title']}")
            logger.info(f"File ID: {file_info['id']}")
            logger.info(f"Link: {file_info['webContentLink']}")
    except Exception as e:
        logger.error(f"Failed to upload to Google Drive: {e}")
        raise


def modify_resolution_column(input_file, output_file, column_name):
    """Modify resolution column (backward compatibility)"""
    return csv_processor.modify_resolution_column(input_file, output_file, column_name)


################################################# main function here ###########################


def main_msoc_process():
    """Main MSOC processing function with comprehensive error handling"""
    try:
        logger.info("Starting MSOC processing...")

        # Generate the MSOC CSV
        msoc_export_to_csv()

        # Get file paths using centralized configuration
        current_datetime = datetime.now()
        date_str = current_datetime.strftime("%d%m%y")

        input_msoc_file = config.get_file_path('msoc_csv', date_str)
        output_msoc_file = config.export_dir / "modified_msoc.csv"
        date_converted_msoc = config.export_dir / 'date_conv_msoc.csv'
        excel_file = config.get_file_path('excel', date_str, export_type='msoc')

        # Validate input file exists and has content
        if not input_msoc_file.exists():
            error_msg = f"MSOC export file not found: {input_msoc_file}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        # Check file content
        if input_msoc_file.stat().st_size == 0:
            error_msg = f"MSOC export file is empty: {input_msoc_file}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        logger.info(f"Processing MSOC file: {input_msoc_file}")

        # Process the file through various transformations using utilities
        csv_processor.modify_resolution_column(
            str(input_msoc_file),
            str(output_msoc_file),
            'Custom field (Resolutions Notes)'
        )

        csv_processor.rearrange_columns(
            str(output_msoc_file),
            str(output_msoc_file),
            config.msoc_column_order
        )

        csv_processor.convert_date_format(
            str(output_msoc_file),
            str(date_converted_msoc)
        )

        # Create Excel file with formulas using new utility
        excel_processor.transform_msoc_csv_to_excel(
            str(date_converted_msoc),
            str(excel_file)
        )

        # Upload to Google Drive
        upload_to_drive(str(excel_file))

        logger.info("MSOC processing completed successfully")

    except Exception as e:
        logger.error(f"Critical error in MSOC processing: {e}")
        raise



def main_pdrm_process():
    # generate the pdrm fw csv
    pdrm_fw_export_to_csv()

    excel_dir= f"{os.getcwd()}/EXPORT" # directory to store the excel sheet

    desired_column_order = ['Issue key', 'Custom field (Client Ticket Number)', 'Custom field (Notification Sent out Date/Time)', 'Custom field (Last Follow Up)', 'Priority', 'Custom field (Analyst Name)', 'Summary', 'Custom field (PDRM Fault Category)', 'Custom field (Closing Time)', 'Custom field (PDRM Acknowledge Venue)', 'Status', 'Custom field (Resolutions Notes)', 'Custom field (TM/PDRM Holding Time)', 'Custom field (Total Time Taken)', 'Custom field (PDRM Acknowledge Time)', 'Custom field (PDRM TL Holding Time)', 'Custom field (Customer Organization)', 'Custom field (Month)','Comment']

    current_datetime = datetime.now()
    curr_fmt_datetime =  current_datetime.strftime("%d%m%y")
    curr_pdrm_file = curr_fmt_datetime + "_pdrm_"

    input_pdrm_file = f"{excel_dir}/{curr_pdrm_file}.csv" # how to obtain the msoc csv file?
    output_pdrm_file = f"{excel_dir}/modified_pdrm.csv"

    modify_resolution_column(input_pdrm_file, output_pdrm_file,column_name='Comment')

    rearrange_columns(output_pdrm_file, output_pdrm_file, desired_column_order)

    date_converted_pdrm = f'{excel_dir}/date_conv_pdrm.csv'  # Replace with the desired output CSV file
    convert_date_format(input_file=output_pdrm_file, output_file=date_converted_pdrm)

    modified_pdrm_excel = f"{excel_dir}/modified_pdrm_{curr_fmt_datetime}.xlsx"
    transform__pdrm_csv_to_excel_with_formulas(date_converted_pdrm,modified_pdrm_excel)

    # gdrive upload directory
    upload_to_drive(modified_pdrm_excel)


def main_pdrm_structured_process():
    """
    Enhanced PDRM process using the new structured data format
    """
    print("Starting PDRM Firewall structured export process...")

    # Generate structured PDRM Firewall export
    structured_csv, structured_json = pdrm_fw_export_structured()

    if structured_csv and structured_json:
        print(f"Structured export completed successfully:")
        print(f"  CSV: {structured_csv}")
        print(f"  JSON: {structured_json}")

        # Optional: Upload structured files to Google Drive
        try:
            upload_to_drive(structured_csv)
            upload_to_drive(structured_json)
            print("Files uploaded to Google Drive successfully")
        except Exception as e:
            print(f"Warning: Failed to upload to Google Drive: {str(e)}")
    else:
        print("Structured export failed")


def validate_and_show_client_config():
    """
    Validate and display current client configuration
    """
    from ..utils.data_validation import DataValidator

    validator = DataValidator()

    # Validate client configuration
    validation_results = validator.validate_client_configuration()

    # Generate and display report
    report = validator.generate_validation_report(validation_results, "client_config")
    print(report)

    # Show current client lists
    print("\n=== CURRENT CLIENT CONFIGURATION ===")
    print(f"All Clients ({len(ClientConfig.get_all_clients())}):")
    for client in ClientConfig.get_all_clients():
        client_type = ClientConfig.ALL_CLIENTS[client].value
        print(f"  - {client} ({client_type})")

    print(f"\nMSOC JQL Query Client List:")
    print(f"  {ClientConfig.get_jql_client_list()}")

    return validation_results['valid']

if __name__ == "__main__":
    logger.info("=== SOC Jira Tracker - Enhanced Version ===")
    logger.info("NOTE: make sure the current file in the gdrive is moved to the archive folder")

    try:
        # Ensure export directory exists first
        ensure_export_directory()

        # Validate system configuration first
        logger.info("1. Validating system configuration...")
        config_valid = validate_and_show_client_config()

        if not config_valid:
            logger.error("System configuration validation failed. Please check the configuration.")
            sys.exit(1)

        # Configuration flags
        pdrm_trigger = True
        msoc_trigger = True
        use_structured_pdrm = True  # New flag for structured PDRM export

        logger.info("2. Export Configuration:")
        logger.info(f"   - MSOC Export: {'Enabled' if msoc_trigger else 'Disabled'}")
        logger.info(f"   - PDRM Export: {'Enabled' if pdrm_trigger else 'Disabled'}")
        logger.info(f"   - PDRM Structured Export: {'Enabled' if use_structured_pdrm else 'Disabled'}")

        # Run exports
        if msoc_trigger:
            logger.info("3. Running MSOC export process...")
            try:
                main_msoc_process()
                logger.info("   MSOC export completed successfully")
            except Exception as e:
                logger.error(f"   ERROR in MSOC export: {str(e)}")

        if pdrm_trigger:
            logger.info("4. Running PDRM export process...")
            try:
                if use_structured_pdrm:
                    main_pdrm_structured_process()
                    logger.info("   PDRM structured export completed successfully")
                else:
                    main_pdrm_process()
                    logger.info("   PDRM traditional export completed successfully")
            except Exception as e:
                logger.error(f"   ERROR in PDRM export: {str(e)}")

        # Cleanup
        logger.info("5. Cleaning up temporary files...")
        export_dir = Path("EXPORT")
        delete_current_day_csv_files(str(export_dir))
        logger.info("   Cleanup completed")

        logger.info("=== Export Process Completed ===")
        logger.info("Check the EXPORT directory for generated files.")
        logger.info("Structured PDRM files are available in both CSV and JSON formats.")

    except Exception as e:
        logger.error(f"Critical error in main process: {e}")
        sys.exit(1)
    print("Check the EXPORT directory for generated files.")
    print("Structured PDRM files are available in both CSV and JSON formats.")









