"""
Test module for data validation functionality
"""

import unittest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.utils.data_validation import DataValidator


class TestDataValidation(unittest.TestCase):
    """Test cases for DataValidator class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.validator = DataValidator()
    
    def test_validate_client_configuration(self):
        """Test client configuration validation"""
        result = self.validator.validate_client_configuration()
        self.assertIsInstance(result, dict)
        self.assertIn('valid', result)
        self.assertIn('total_clients', result)
        self.assertIn('msoc_clients', result)
        self.assertIn('firewall_clients', result)
    
    def test_validate_pdrm_firewall_data(self):
        """Test PDRM Firewall data validation"""
        # Test with valid data structure
        valid_data = [{
            'issue_key': 'TEST-123',
            'summary': 'Test Issue',
            'status': 'Open'
        }]

        result = self.validator.validate_pdrm_firewall_data(valid_data)
        self.assertIsInstance(result, dict)
        self.assertIn('valid', result)
        self.assertIn('total_records', result)

        # Test with empty data
        empty_data = []
        result = self.validator.validate_pdrm_firewall_data(empty_data)
        self.assertIsInstance(result, dict)
        self.assertEqual(result['total_records'], 0)


if __name__ == '__main__':
    unittest.main()
