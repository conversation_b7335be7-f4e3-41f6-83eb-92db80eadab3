"""
Test module for client configuration functionality
"""

import unittest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.config.client_config import ClientConfig, ClientType


class TestClientConfig(unittest.TestCase):
    """Test cases for ClientConfig class"""
    
    def test_get_all_clients(self):
        """Test getting all clients"""
        clients = ClientConfig.get_all_clients()
        self.assertIsInstance(clients, list)
        self.assertGreater(len(clients), 0)
        self.assertIn("MAG", clients)
        self.assertIn("PDRM", clients)
    
    def test_get_msoc_clients(self):
        """Test getting MSOC clients"""
        msoc_clients = ClientConfig.get_msoc_clients()
        self.assertIsInstance(msoc_clients, list)
        self.assertIn("MAG", msoc_clients)
        self.assertIn("PDRM", msoc_clients)
    
    def test_get_firewall_clients(self):
        """Test getting firewall clients"""
        firewall_clients = ClientConfig.get_firewall_clients()
        self.assertIsInstance(firewall_clients, list)
        self.assertIn("PDRM Firewall", firewall_clients)
    
    def test_validate_client_list(self):
        """Test client list validation"""
        valid_clients = ["MAG", "PDRM"]
        invalid_clients = ["NonExistent"]
        mixed_clients = ["MAG", "NonExistent"]

        # Test valid clients
        result = ClientConfig.validate_client_list(valid_clients)
        self.assertEqual(len(result['valid']), 2)
        self.assertEqual(len(result['invalid']), 0)
        self.assertIn("MAG", result['valid'])
        self.assertIn("PDRM", result['valid'])

        # Test invalid clients
        result = ClientConfig.validate_client_list(invalid_clients)
        self.assertEqual(len(result['valid']), 0)
        self.assertIn("NonExistent", result['invalid'])

        # Test mixed clients
        result = ClientConfig.validate_client_list(mixed_clients)
        self.assertEqual(len(result['valid']), 1)
        self.assertEqual(len(result['invalid']), 1)
        self.assertIn("NonExistent", result['invalid'])
        self.assertIn("MAG", result['valid'])
        self.assertNotIn("MAG", result['invalid'])
    
    def test_get_jql_client_list(self):
        """Test JQL client list generation"""
        clients = ["MAG", "PDRM", "EPF Guardium"]
        jql_list = ClientConfig.get_jql_client_list(clients)
        
        # Should quote clients with spaces
        self.assertIn('"EPF Guardium"', jql_list)
        self.assertIn('MAG', jql_list)
        self.assertIn('PDRM', jql_list)


if __name__ == '__main__':
    unittest.main()
