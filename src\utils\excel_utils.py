"""
Excel Utilities Module for SOC Jira Tracker

This module provides utilities for Excel file generation, formula creation,
and data transformation from CSV to Excel formats.
"""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import pandas as pd

from ..config.app_config import get_config

logger = logging.getLogger(__name__)


class ExcelProcessingError(Exception):
    """Custom exception for Excel processing errors"""
    pass


class ExcelFormulaBuilder:
    """
    Builder class for creating Excel formulas
    """
    
    def __init__(self):
        self.config = get_config()
    
    def build_sla_status_formula(self, priority_col: str, notification_col: str, 
                                alert_col: str, row: int) -> str:
        """
        Build SLA status formula based on priority and time thresholds
        
        Args:
            priority_col: Priority column letter (e.g., 'J')
            notification_col: Notification column letter
            alert_col: Alert column letter
            row: Row number
            
        Returns:
            Excel formula string
        """
        high_threshold = self.config.sla_thresholds['high_priority']
        medium_threshold = self.config.sla_thresholds['medium_priority']
        low_threshold = self.config.sla_thresholds['low_priority']
        
        high_priorities = '","'.join(self.config.priority_levels['high'])
        medium_priorities = '","'.join(self.config.priority_levels['medium'])
        low_priorities = '","'.join(self.config.priority_levels['low'])
        
        formula = (
            f'=IF(LEN({priority_col}{row}), '
            f'IF(OR('
            f'AND(OR({priority_col}{row}="{high_priorities}"), '
            f'{notification_col}{row}-{alert_col}{row}<TIME({high_threshold},0,1)), '
            f'AND({priority_col}{row}="{medium_priorities}", '
            f'{notification_col}{row}-{alert_col}{row}<TIME({medium_threshold},0,1)), '
            f'AND(OR({priority_col}{row}="{low_priorities}"), '
            f'{notification_col}{row}-{alert_col}{row}<TIME({low_threshold},0,1))'
            f'), "Met", "Not Met"), "")'
        )
        
        return formula
    
    def build_duration_formula(self, notification_col: str, alert_col: str, row: int) -> str:
        """
        Build duration calculation formula
        
        Args:
            notification_col: Notification column letter
            alert_col: Alert column letter
            row: Row number
            
        Returns:
            Excel formula string
        """
        formula = (
            f'=IF({alert_col}{row}="", "", '
            f'IF({notification_col}{row}>{alert_col}{row}, '
            f'TEXT({notification_col}{row}-{alert_col}{row},"hh:mm:ss"), '
            f'TEXT({notification_col}{row}-{alert_col}{row}+1,"hh:mm:ss")))'
        )
        
        return formula
    
    def build_total_time_formula(self, closing_col: str, start_col: str, row: int) -> str:
        """
        Build total time taken formula
        
        Args:
            closing_col: Closing time column letter
            start_col: Start time column letter
            row: Row number
            
        Returns:
            Excel formula string
        """
        return f'=SUM({closing_col}{row}-{start_col}{row}),"[hh]:mm:ss")'
    
    def build_holding_time_formula(self, total_col: str, tm_col: str, row: int) -> str:
        """
        Build holding time formula
        
        Args:
            total_col: Total time column letter
            tm_col: TM time column letter
            row: Row number
            
        Returns:
            Excel formula string
        """
        formula = (
            f'=IF({total_col}{row}=0, 0, '
            f'IF(OR(ISBLANK({tm_col}{row}), {tm_col}{row}=0), 0, '
            f'TEXT(({total_col}{row}-{tm_col}{row}), "[hh]:mm:ss")))'
        )
        
        return formula


class ExcelProcessor:
    """
    Main Excel processing class
    """
    
    def __init__(self):
        self.config = get_config()
        self.formula_builder = ExcelFormulaBuilder()
    
    def get_column_letter(self, index: int) -> str:
        """
        Convert column index to Excel column letter
        
        Args:
            index: Column index (0-based)
            
        Returns:
            Column letter (e.g., 'A', 'B', 'AA')
        """
        if index < 26:
            return chr(65 + index)
        else:
            # Handle columns beyond Z (AA, AB, etc.)
            first_letter = chr(64 + (index // 26))
            second_letter = chr(65 + (index % 26))
            return first_letter + second_letter
    
    def transform_msoc_csv_to_excel(self, csv_file: str, excel_file: str) -> bool:
        """
        Transform MSOC CSV to Excel with formulas
        
        Args:
            csv_file: Path to input CSV file
            excel_file: Path to output Excel file
            
        Returns:
            True if successful
            
        Raises:
            ExcelProcessingError: If processing fails
        """
        try:
            df = pd.read_csv(csv_file)
            
            # Check required columns exist
            required_columns = [
                'Custom field (SLA Status)',
                'Custom field (Duration)',
                'Custom field (Notification Sent out Date/Time)',
                'Custom field (Alert Generation Date/Time)',
                'Priority'
            ]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ExcelProcessingError(f"Missing required columns: {missing_columns}")
            
            # Get column indices
            sla_status_index = df.columns.get_loc('Custom field (SLA Status)')
            duration_index = df.columns.get_loc('Custom field (Duration)')
            notification_index = df.columns.get_loc('Custom field (Notification Sent out Date/Time)')
            alert_index = df.columns.get_loc('Custom field (Alert Generation Date/Time)')
            priority_index = df.columns.get_loc('Priority')
            
            # Convert indices to column letters
            sla_col = self.get_column_letter(sla_status_index)
            duration_col = self.get_column_letter(duration_index)
            notification_col = self.get_column_letter(notification_index)
            alert_col = self.get_column_letter(alert_index)
            priority_col = self.get_column_letter(priority_index)
            
            # Create Excel file with formulas
            with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
                df.to_excel(writer, index=False, sheet_name='Sheet1')
                
                workbook = writer.book
                worksheet = writer.sheets['Sheet1']
                
                last_row = len(df) + 1
                
                # Add SLA Status formulas
                for row in range(2, last_row + 1):
                    formula = self.formula_builder.build_sla_status_formula(
                        priority_col, notification_col, alert_col, row
                    )
                    worksheet.write_formula(f'{sla_col}{row}', formula)
                
                # Add Duration formulas
                for row in range(2, last_row + 1):
                    formula = self.formula_builder.build_duration_formula(
                        notification_col, alert_col, row
                    )
                    worksheet.write_formula(f'{duration_col}{row}', formula)
                
                # Auto-fit column widths
                self._autofit_columns(worksheet, df)
            
            logger.info(f"MSOC Excel file created successfully: {excel_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating MSOC Excel file: {e}")
            raise ExcelProcessingError(f"Failed to create MSOC Excel file: {e}")
    
    def transform_pdrm_csv_to_excel(self, csv_file: str, excel_file: str) -> bool:
        """
        Transform PDRM CSV to Excel with formulas
        
        Args:
            csv_file: Path to input CSV file
            excel_file: Path to output Excel file
            
        Returns:
            True if successful
            
        Raises:
            ExcelProcessingError: If processing fails
        """
        try:
            df = pd.read_csv(csv_file)
            
            # Check required columns exist
            required_columns = [
                'Custom field (TM/PDRM Holding Time)',
                'Custom field (Total Time Taken)',
                'Custom field (Closing Time)',
                'Custom field (Notification Sent out Date/Time)',
                'Custom field (PDRM TL Holding Time)',
                'Custom field (PDRM Acknowledge Time)'
            ]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ExcelProcessingError(f"Missing required columns: {missing_columns}")
            
            # Get column indices
            holding_time_index = df.columns.get_loc('Custom field (TM/PDRM Holding Time)')
            total_time_index = df.columns.get_loc('Custom field (Total Time Taken)')
            closing_time_index = df.columns.get_loc('Custom field (Closing Time)')
            start_time_index = df.columns.get_loc('Custom field (Notification Sent out Date/Time)')
            pdrm_holding_index = df.columns.get_loc('Custom field (PDRM TL Holding Time)')
            tm_acknowledge_index = df.columns.get_loc('Custom field (PDRM Acknowledge Time)')
            
            # Convert indices to column letters
            holding_col = self.get_column_letter(holding_time_index)
            total_col = self.get_column_letter(total_time_index)
            closing_col = self.get_column_letter(closing_time_index)
            start_col = self.get_column_letter(start_time_index)
            pdrm_col = self.get_column_letter(pdrm_holding_index)
            tm_col = self.get_column_letter(tm_acknowledge_index)
            
            # Create Excel file with formulas
            with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
                df.to_excel(writer, index=False, sheet_name='Sheet1')
                
                workbook = writer.book
                worksheet = writer.sheets['Sheet1']
                
                last_row = len(df) + 1
                
                # Add Total Time formulas
                for row in range(2, last_row + 1):
                    formula = self.formula_builder.build_total_time_formula(
                        closing_col, start_col, row
                    )
                    worksheet.write_formula(f'{total_col}{row}', formula)
                
                # Add Holding Time formulas
                for row in range(2, last_row + 1):
                    formula = self.formula_builder.build_holding_time_formula(
                        total_col, tm_col, row
                    )
                    worksheet.write_formula(f'{holding_col}{row}', formula)
                
                # Auto-fit column widths
                self._autofit_columns(worksheet, df)
            
            logger.info(f"PDRM Excel file created successfully: {excel_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating PDRM Excel file: {e}")
            raise ExcelProcessingError(f"Failed to create PDRM Excel file: {e}")
    
    def _autofit_columns(self, worksheet, df: pd.DataFrame):
        """
        Auto-fit column widths in Excel worksheet
        
        Args:
            worksheet: xlsxwriter worksheet object
            df: DataFrame with data
        """
        try:
            for i, col in enumerate(df.columns):
                # Calculate column width based on content
                max_length = max(
                    df[col].astype(str).map(len).max(),  # Max content length
                    len(col)  # Header length
                )
                # Set reasonable limits
                column_width = min(max_length + 2, 50)  # Max width of 50
                worksheet.set_column(i, i, column_width)
        except Exception as e:
            logger.warning(f"Could not auto-fit columns: {e}")


# Global processor instance
_excel_processor_instance = None


def get_excel_processor() -> ExcelProcessor:
    """
    Get the global ExcelProcessor instance (singleton pattern)
    
    Returns:
        ExcelProcessor instance
    """
    global _excel_processor_instance
    if _excel_processor_instance is None:
        _excel_processor_instance = ExcelProcessor()
    return _excel_processor_instance


if __name__ == "__main__":
    # Test Excel processor
    processor = get_excel_processor()
    print("✅ Excel processor initialized successfully")
