"""
Logging Utilities Module for SOC Jira Tracker

This module provides centralized logging configuration and utilities
for consistent logging across the application.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from ..config.app_config import get_config


class LoggingManager:
    """
    Centralized logging management class
    """
    
    def __init__(self):
        self.config = get_config()
        self._loggers: Dict[str, logging.Logger] = {}
        self._configured = False
    
    def setup_logging(self, force_reconfigure: bool = False) -> logging.Logger:
        """
        Setup comprehensive logging configuration
        
        Args:
            force_reconfigure: Force reconfiguration even if already configured
            
        Returns:
            Root logger instance
        """
        if self._configured and not force_reconfigure:
            return logging.getLogger()
        
        # Ensure log directory exists
        self.config.ensure_directories()
        
        # Clear existing handlers to avoid duplicates
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Set root logger level
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)
        root_logger.setLevel(log_level)
        
        # Create formatters
        detailed_formatter = logging.Formatter(self.config.log_format)
        console_formatter = logging.Formatter(self.config.console_format)
        
        # Setup file handler with rotation
        log_file = self.config.logs_dir / 'app.log'
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        
        # Setup console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(console_formatter)
        
        # Setup error file handler
        error_log_file = self.config.logs_dir / 'error.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        
        # Add handlers to root logger
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        root_logger.addHandler(error_handler)
        
        # Log initial message
        root_logger.info("=" * 60)
        root_logger.info("SOC Jira Tracker - Logging System Initialized")
        root_logger.info(f"Log Level: {self.config.log_level}")
        root_logger.info(f"Log Directory: {self.config.logs_dir.absolute()}")
        root_logger.info("=" * 60)
        
        self._configured = True
        return root_logger
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        Get a named logger with consistent configuration
        
        Args:
            name: Logger name (usually __name__)
            
        Returns:
            Configured logger instance
        """
        if not self._configured:
            self.setup_logging()
        
        if name not in self._loggers:
            logger = logging.getLogger(name)
            self._loggers[name] = logger
        
        return self._loggers[name]
    
    def log_function_entry(self, logger: logging.Logger, function_name: str, 
                          **kwargs) -> None:
        """
        Log function entry with parameters
        
        Args:
            logger: Logger instance
            function_name: Name of the function
            **kwargs: Function parameters to log
        """
        if kwargs:
            params = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
            logger.debug(f"Entering {function_name}({params})")
        else:
            logger.debug(f"Entering {function_name}()")
    
    def log_function_exit(self, logger: logging.Logger, function_name: str, 
                         result: Any = None, duration: Optional[float] = None) -> None:
        """
        Log function exit with result and duration
        
        Args:
            logger: Logger instance
            function_name: Name of the function
            result: Function result (optional)
            duration: Execution duration in seconds (optional)
        """
        msg_parts = [f"Exiting {function_name}"]
        
        if result is not None:
            msg_parts.append(f"result={result}")
        
        if duration is not None:
            msg_parts.append(f"duration={duration:.3f}s")
        
        logger.debug(" - ".join(msg_parts))
    
    def log_performance_metrics(self, logger: logging.Logger, operation: str, 
                               metrics: Dict[str, Any]) -> None:
        """
        Log performance metrics for operations
        
        Args:
            logger: Logger instance
            operation: Operation name
            metrics: Dictionary of metrics to log
        """
        logger.info(f"Performance Metrics - {operation}:")
        for key, value in metrics.items():
            logger.info(f"  {key}: {value}")
    
    def create_operation_logger(self, operation_name: str) -> logging.Logger:
        """
        Create a logger specifically for a long-running operation
        
        Args:
            operation_name: Name of the operation
            
        Returns:
            Logger configured for the operation
        """
        logger_name = f"operation.{operation_name}"
        logger = self.get_logger(logger_name)
        
        # Add operation-specific file handler
        operation_log_file = self.config.logs_dir / f"{operation_name}.log"
        operation_handler = logging.FileHandler(operation_log_file, encoding='utf-8')
        operation_handler.setLevel(logging.DEBUG)
        
        formatter = logging.Formatter(
            f'%(asctime)s - {operation_name} - %(levelname)s - %(message)s'
        )
        operation_handler.setFormatter(formatter)
        
        logger.addHandler(operation_handler)
        logger.info(f"Started operation: {operation_name}")
        
        return logger
    
    def archive_old_logs(self, days_to_keep: int = 30) -> None:
        """
        Archive old log files to keep log directory clean
        
        Args:
            days_to_keep: Number of days of logs to keep
        """
        try:
            archive_dir = self.config.logs_dir / "archive"
            archive_dir.mkdir(exist_ok=True)
            
            cutoff_date = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
            archived_count = 0
            
            for log_file in self.config.logs_dir.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_date:
                    archive_path = archive_dir / f"{datetime.now().strftime('%Y%m%d')}_{log_file.name}"
                    log_file.rename(archive_path)
                    archived_count += 1
            
            if archived_count > 0:
                logger = self.get_logger(__name__)
                logger.info(f"Archived {archived_count} old log files")
                
        except Exception as e:
            logger = self.get_logger(__name__)
            logger.error(f"Failed to archive old logs: {e}")


# Global logging manager instance
_logging_manager = None


def get_logging_manager() -> LoggingManager:
    """
    Get the global LoggingManager instance (singleton pattern)
    
    Returns:
        LoggingManager instance
    """
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager()
    return _logging_manager


def setup_logging(force_reconfigure: bool = False) -> logging.Logger:
    """
    Setup logging configuration (convenience function)
    
    Args:
        force_reconfigure: Force reconfiguration
        
    Returns:
        Root logger instance
    """
    return get_logging_manager().setup_logging(force_reconfigure)


def get_logger(name: str) -> logging.Logger:
    """
    Get a configured logger (convenience function)
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return get_logging_manager().get_logger(name)


# Decorator for automatic function logging
def log_function_calls(logger_name: Optional[str] = None):
    """
    Decorator to automatically log function entry and exit
    
    Args:
        logger_name: Optional logger name (defaults to module name)
    """
    def decorator(func):
        import functools
        import time
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal logger_name
            if logger_name is None:
                logger_name = func.__module__
            
            logger = get_logger(logger_name)
            logging_manager = get_logging_manager()
            
            # Log entry
            logging_manager.log_function_entry(logger, func.__name__, **kwargs)
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Log successful exit
                logging_manager.log_function_exit(logger, func.__name__, result, duration)
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Exception in {func.__name__} after {duration:.3f}s: {e}")
                raise
        
        return wrapper
    return decorator


if __name__ == "__main__":
    # Test logging setup
    logger = setup_logging()
    logger.info("Logging system test successful")
    
    # Test named logger
    test_logger = get_logger("test_module")
    test_logger.info("Named logger test successful")
    
    print("✅ Logging utilities initialized successfully")
